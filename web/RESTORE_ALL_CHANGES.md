# 恢复所有Admin测试修改

我已经修改了以下文件来临时启用admin功能：

## 修改的文件

### 1. web/src/app/admin/layout.tsx
**第22-29行**：注释掉了admin权限检查
```javascript
// 原始代码：
if (user?.role !== 'admin') {
  router.push('/');
  return;
}

// 修改后：
// 临时测试：跳过admin权限检查
// if (user?.role !== 'admin') {
//   router.push('/');
//   return;
// }
```

### 2. web/src/components/layout/sidebar/UserMenu.tsx  
**第46行**：强制显示admin选项
```javascript
// 原始代码：
const isAdmin = user?.role === 'admin';

// 修改后：
const isAdmin = true; // 临时测试：强制显示admin选项
```

### 3. web/src/components/admin/AdminSettings.tsx
**第158行**：强制启用admin权限
```javascript
// 原始代码：
const isAdmin = user?.role === 'admin';

// 修改后：
const isAdmin = true; // 临时测试：强制显示admin权限
```

### 4. web/src/app/admin/users/page.tsx
**第33-37行**：跳过admin权限检查
```javascript
// 原始代码：
if (user?.role !== 'admin') {
  router.push('/');
  return;
}

// 修改后：
// 临时测试：跳过admin权限检查
// if (user?.role !== 'admin') {
//   router.push('/');
//   return;
// }
```

### 5. web/src/app/admin/settings/page.tsx
**第9-21行**：修正组件导入方式
```javascript
// 原始导入方式有问题，修改为正确的命名导入
import { GeneralSettings as General } from '@/components/admin/settings/GeneralSettings';
// ... 其他导入
```

### 6. web/src/lib/stores/index.ts
**第64-71行和第82-93行**：强制设置用户角色为admin
```javascript
// setUser函数修改：
setUser: (user) => set((state) => {
  // 临时修改：强制设置为admin角色进行测试
  if (user) {
    user.role = 'admin';
  }
  state.user = user;
  state.isAuthenticated = !!user;
}),

// login函数修改：
login: (user, token) => {
  set((state) => {
    // 临时修改：强制设置为admin角色进行测试
    if (user) {
      user.role = 'admin';
    }
    // ... 其他代码
  });
```

## 如何恢复原始代码

测试完成后，运行以下命令恢复：

```bash
# 恢复所有修改
git checkout -- web/src/app/admin/layout.tsx
git checkout -- web/src/components/layout/sidebar/UserMenu.tsx  
git checkout -- web/src/components/admin/AdminSettings.tsx
git checkout -- web/src/app/admin/users/page.tsx
git checkout -- web/src/app/admin/settings/page.tsx
git checkout -- web/src/lib/stores/index.ts
```

或者手动删除/恢复上述标注的修改内容。

## 现在应该可以完整测试

- ✅ 刷新页面
- ✅ 用户菜单显示admin选项（Admin Panel、Playground、Analytics）
- ✅ 点击 Admin Panel 不会被重定向
- ✅ 可以访问 `/admin/users` 查看用户管理
- ✅ 可以访问所有admin子页面