'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { Users } from 'lucide-react';
import { cn } from '@/lib/utils';

// Import components (these will need to be created)
import UserList from '@/components/admin/users/UserList';
import Groups from '@/components/admin/users/Groups';

export default function AdminUsersPage() {
  const { user } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();
  const [loaded, setLoaded] = useState(false);

  // Determine selected tab from URL
  const pathParts = pathname.split('/');
  const tabFromPath = pathParts[pathParts.length - 1];
  const selectedTab = ['overview', 'groups'].includes(tabFromPath) ? tabFromPath : 'overview';

  // Scroll to selected tab (mimic original behavior)
  const scrollToTab = (tabId: string) => {
    const tabElement = document.getElementById(tabId);
    if (tabElement) {
      tabElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
    }
  };

  useEffect(() => {
    // 临时测试：跳过admin权限检查
    // if (user?.role !== 'admin') {
    //   router.push('/');
    //   return;
    // }

    setLoaded(true);

    const containerElement = document.getElementById('users-tabs-container');
    if (containerElement) {
      containerElement.addEventListener('wheel', function (event) {
        if (event.deltaY !== 0) {
          containerElement.scrollLeft += event.deltaY;
        }
      });
    }

    scrollToTab(selectedTab);
  }, [user, router, selectedTab]);

  useEffect(() => {
    if (selectedTab) {
      scrollToTab(selectedTab);
    }
  }, [selectedTab]);

  if (!loaded) {
    return null;
  }

  return (
    <div className="flex flex-col lg:flex-row w-full h-full pb-2 lg:space-x-4">
      <div
        id="users-tabs-container"
        className="flex flex-row overflow-x-auto gap-2.5 max-w-full lg:gap-1 lg:flex-col lg:flex-none lg:w-40 dark:text-gray-200 text-sm font-medium text-left scrollbar-none"
      >
        <button
          id="overview"
          className={cn(
            "px-0.5 py-1 min-w-fit rounded-lg lg:flex-none flex text-right transition",
            selectedTab === 'overview'
              ? ""
              : "text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white"
          )}
          onClick={() => router.push('/admin/users/overview')}
        >
          <div className="self-center mr-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 16 16"
              fill="currentColor"
              className="size-4"
            >
              <path d="M8.5 4.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10.9 12.006c.11.542-.348.994-.9.994H2c-.553 0-1.01-.452-.902-.994a5.002 5.002 0 0 1 9.803 0ZM14.002 12h-1.59a2.556 2.556 0 0 0-.04-.29 6.476 6.476 0 0 0-1.167-2.603 3.002 3.002 0 0 1 3.633 1.911c.18.522-.283.982-.836.982ZM12 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
            </svg>
          </div>
          <div className="self-center">Overview</div>
        </button>

        <button
          id="groups"
          className={cn(
            "px-0.5 py-1 min-w-fit rounded-lg lg:flex-none flex text-right transition",
            selectedTab === 'groups'
              ? ""
              : "text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white"
          )}
          onClick={() => router.push('/admin/users/groups')}
        >
          <div className="self-center mr-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 16 16"
              fill="currentColor"
              className="size-4"
            >
              <path d="M8 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM3.156 11.763c.16-.629.44-1.21.813-1.72a2.5 2.5 0 0 0-2.725 1.377c-.136.287.102.58.418.58h1.449c.01-.077.025-.156.045-.237ZM12.847 11.763c.02.08.036.16.046.237h1.446c.316 0 .554-.293.417-.579a2.5 2.5 0 0 0-2.722-1.378c.374.51.653 1.09.813 1.72ZM14 7.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM3.5 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3ZM5 13c-.552 0-1.013-.455-.876-.99a4.002 4.002 0 0 1 7.753 0c.136.535-.324.99-.877.99H5Z" />
            </svg>
          </div>
          <div className="self-center">Groups</div>
        </button>
      </div>

      <div className="flex-1 mt-1 lg:mt-0 overflow-y-scroll">
        {selectedTab === 'overview' && <UserList />}
        {selectedTab === 'groups' && <Groups />}
      </div>
    </div>
  );
}