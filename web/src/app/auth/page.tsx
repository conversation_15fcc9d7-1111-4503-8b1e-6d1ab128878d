'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore, useAppStore } from '@/lib/stores';
import { userSignIn, userSignUp, ldapUserSignIn, getSessionUser } from '@/lib/api/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/common';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { APP_NAME, WEBUI_BASE_URL } from '@/lib/constants';

type AuthMode = 'signin' | 'signup' | 'ldap';

function AuthPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading, login, setLoading, setError } = useAuthStore();
  const { config } = useAppStore();

  const [mode, setMode] = useState<AuthMode>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [ldapUsername, setLdapUsername] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Check for OAuth callback
  useEffect(() => {
    const checkOAuthCallback = async () => {
      // Check for token in URL hash (OAuth callback from original implementation)
      const hash = window.location.hash;
      if (hash && hash.includes('token=')) {
        const params = new URLSearchParams(hash.substring(1));
        const token = params.get('token');
        
        if (token) {
          try {
            setLoading(true);
            const sessionUser = await getSessionUser(token);
            if (sessionUser) {
              login(sessionUser, token);
              toast.success('Successfully signed in with OAuth');
              const redirectPath = searchParams.get('redirect') || '/';
              // Clear the hash from URL
              window.history.replaceState(null, '', window.location.pathname + window.location.search);
              router.push(redirectPath);
              return;
            }
          } catch (error: any) {
            console.error('OAuth callback error:', error);
            toast.error('OAuth sign in failed');
            // Clear the hash from URL even on error
            window.history.replaceState(null, '', window.location.pathname + window.location.search);
          } finally {
            setLoading(false);
          }
        }
      }
    };

    checkOAuthCallback();
  }, [login, router, searchParams, setLoading]);

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !isLoading) {
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    }
  }, [user, isLoading, router, searchParams]);

  // Set loaded state
  useEffect(() => {
    setLoaded(true);
  }, []);

  const handleSignIn = async () => {
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      const response = await userSignIn(email, password);
      login(response.user, response.token);
      toast.success('Successfully signed in');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'Sign in failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (!name || !email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      const response = await userSignUp(name, email, password);
      login(response.user, response.token);
      toast.success('Successfully signed up');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'Sign up failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleLdapSignIn = async () => {
    if (!ldapUsername || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      const response = await ldapUserSignIn(ldapUsername, password);
      login(response.user, response.token);
      toast.success('Successfully signed in');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'LDAP sign in failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'ldap') {
      await handleLdapSignIn();
    } else if (mode === 'signin') {
      await handleSignIn();
    } else {
      await handleSignUp();
    }
  };

  const handleOAuthSignIn = (provider: string) => {
    // Check if OAuth providers are configured
    const oauthProviders = config?.oauth?.providers || {};
    if (Object.keys(oauthProviders).length === 0) {
      toast.error('OAuth providers are not configured');
      return;
    }

    // Use the same URL format as the original implementation
    const baseUrl = WEBUI_BASE_URL || window.location.origin;
    window.location.href = `${baseUrl}/oauth/${provider}/login`;
  };

  if (!loaded || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <Spinner className="size-8" />
      </div>
    );
  }

  // Don't render if user is already authenticated
  if (user) {
    return null;
  }

  return (
    <div
      className="min-h-screen w-full flex items-center justify-center py-4 px-8"
      style={{ backgroundColor: '#F5F7FB' }}
    >
      <div className="flex flex-col items-center gap-6 w-full max-w-lg">
        {/* Header with Logo and Title */}
        <div className="flex flex-col items-center gap-3 text-center" style={{ width: '160px' }}>
          <div className="flex items-center justify-center mb-2">
            <img
              src="/static/logo.svg"
              alt="K2 Logo"
              className="object-contain"
              style={{ width: '68px', height: '80px' }}
              onError={(e) => {
                // Fallback if logo doesn't exist
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) {
                  fallback.classList.remove('hidden');
                  fallback.classList.add('flex');
                }
              }}
            />
            <div className="hidden w-[68px] h-20 bg-blue-500 rounded-lg items-center justify-center text-2xl font-bold text-white">
              K2
            </div>
          </div>
          <h1
            className="text-black m-0 text-center"
            style={{
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 700,
              fontSize: '26px',
              lineHeight: '1.15'
            }}
          >
            Sign in to K2
          </h1>
        </div>

        {/* Main Form Card */}
        <div
          className="bg-white rounded-[24px] border border-[rgba(210,220,225,0.5)] shadow-[0_4px_12px_rgba(22,22,22,0.08)] flex justify-center items-center w-full"
          style={{ padding: '32px 40px', maxWidth: '560px' }}
        >
          <div className="w-full" style={{ maxWidth: '438px' }}>

              {/* Form Fields */}
              <div className="flex flex-col gap-4 w-full">
                {mode === 'signup' && (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="name"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Name
                    </label>
                    <Input
                      id="name"
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter Your Full Name"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                )}

                {mode === 'ldap' ? (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="ldap-username"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Username
                    </label>
                    <Input
                      id="ldap-username"
                      type="text"
                      value={ldapUsername}
                      onChange={(e) => setLdapUsername(e.target.value)}
                      placeholder="Enter your username"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="email"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Email
                    </label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                )}

                <div className="flex flex-col gap-2 w-full">
                  <label
                    htmlFor="password"
                    className="text-black"
                    style={{
                      fontFamily: 'Helvetica Neue, Arial, sans-serif',
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '1.14',
                      letterSpacing: '-0.01em'
                    }}
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter your password"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 pr-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? (
                        // Eye-off icon (password visible, click to hide)
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/>
                          <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 11 8 11 8a13.16 13.16 0 0 1-1.67 2.68"/>
                          <path d="M6.61 6.61A13.526 13.526 0 0 0 1 12s4 8 11 8a9.74 9.74 0 0 0 5.39-1.61"/>
                          <line x1="2" y1="2" x2="22" y2="22"/>
                        </svg>
                      ) : (
                        // Eye icon (password hidden, click to show)
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>

                {/* Remember me and Forgot password */}
                <div className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="remember"
                      className="w-4 h-4 border-2 border-[#CBD2E0] rounded-sm bg-white focus:ring-2 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="remember"
                      className="text-[#485B64]"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '16px',
                        lineHeight: '1.5',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Remember for 30 days
                    </label>
                  </div>
                  <button
                    type="button"
                    className="text-[#485B64] hover:text-gray-700"
                    style={{
                      fontFamily: 'Helvetica, Arial, sans-serif',
                      fontWeight: 400,
                      fontSize: '16px',
                      lineHeight: '1.5',
                      letterSpacing: '-0.01em'
                    }}
                  >
                    Forgot Password
                  </button>
                </div>
              </div>

              {/* Submit Button and Sign up link */}
              <div className="flex flex-col items-center gap-4 w-full mt-6">
                <Button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: '#0081FB',
                    padding: '16px 24px',
                    fontFamily: 'Inter, Arial, sans-serif',
                    fontWeight: 700,
                    fontSize: '18px',
                    lineHeight: '1.33',
                    letterSpacing: '-0.01em'
                  }}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center gap-2">
                      <Spinner className="size-4" />
                      <span>
                        {mode === 'signin' && 'Signing in...'}
                        {mode === 'signup' && 'Creating account...'}
                        {mode === 'ldap' && 'Signing in...'}
                      </span>
                    </div>
                  ) : (
                    <span>
                      {mode === 'signin' && 'Sign in'}
                      {mode === 'signup' && 'Create Account'}
                      {mode === 'ldap' && 'Sign in'}
                    </span>
                  )}
                </Button>

                <div className="flex flex-col items-center gap-2">
                  {mode === 'signin' && (config?.enable_signup !== false) && (
                    <button
                      type="button"
                      onClick={() => setMode('signup')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Don't have an account? Sign up
                    </button>
                  )}
                  
                  {mode === 'signup' && (
                    <button
                      type="button"
                      onClick={() => setMode('signin')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Already have an account? Sign in
                    </button>
                  )}

                  {config?.features?.enable_ldap && mode !== 'ldap' && (
                    <button
                      type="button"
                      onClick={() => setMode('ldap')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Sign in with LDAP
                    </button>
                  )}

                  {mode === 'ldap' && (
                    <button
                      type="button"
                      onClick={() => setMode('signin')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Back to email sign in
                    </button>
                  )}
                </div>
              </div>

              {/* OR Divider - only show if OAuth providers are available */}
              {config?.oauth?.providers && Object.keys(config.oauth.providers).length > 0 && (
                <div className="inline-flex items-center justify-center w-full">
                  <hr className="w-32 h-px my-7 border-0 bg-gray-700/10 dark:bg-gray-100/10" />
                  <span className="px-3 text-sm font-medium text-gray-900 dark:text-white bg-transparent">
                    or
                  </span>
                  <hr className="w-32 h-px my-7 border-0 bg-gray-700/10 dark:bg-gray-100/10" />
                </div>
              )}

              {/* Social Login Buttons */}
              {config?.oauth?.providers && Object.keys(config.oauth.providers).length > 0 && (
                <div className="flex flex-col space-y-2">
                  {/* Google */}
                  {config.oauth.providers.google && (
                    <button
                      type="button"
                      onClick={() => handleOAuthSignIn('google')}
                      disabled={isSubmitting}
                      className="flex justify-center items-center bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="size-6 mr-3">
                        <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"/>
                        <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"/>
                        <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"/>
                        <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"/>
                        <path fill="none" d="M0 0h48v48H0z"/>
                      </svg>
                      <span>Continue with Google</span>
                    </button>
                  )}

                  {/* Microsoft */}
                  {config.oauth.providers.microsoft && (
                    <button
                      type="button"
                      onClick={() => handleOAuthSignIn('microsoft')}
                      disabled={isSubmitting}
                      className="flex justify-center items-center bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 21" className="size-6 mr-3">
                        <rect x="1" y="1" width="9" height="9" fill="#f25022"/>
                        <rect x="1" y="11" width="9" height="9" fill="#00a4ef"/>
                        <rect x="11" y="1" width="9" height="9" fill="#7fba00"/>
                        <rect x="11" y="11" width="9" height="9" fill="#ffb900"/>
                      </svg>
                      <span>Continue with Microsoft</span>
                    </button>
                  )}

                  {/* GitHub */}
                  {config.oauth.providers.github && (
                    <button
                      type="button"
                      onClick={() => handleOAuthSignIn('github')}
                      disabled={isSubmitting}
                      className="flex justify-center items-center bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="size-6 mr-3">
                        <path fill="currentColor" d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.92 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57C20.565 21.795 24 17.31 24 12c0-6.63-5.37-12-12-12z"/>
                      </svg>
                      <span>Continue with GitHub</span>
                    </button>
                  )}

                  {/* OIDC */}
                  {config.oauth.providers.oidc && (
                    <button
                      type="button"
                      onClick={() => handleOAuthSignIn('oidc')}
                      disabled={isSubmitting}
                      className="flex justify-center items-center bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6 mr-3">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"/>
                      </svg>
                      <span>Continue with SSO</span>
                    </button>
                  )}
                </div>
              )}
          </div>
        </div>

        {/* Terms and Privacy */}
        <p
          className="text-center text-[#79817D] max-w-md"
          style={{
            fontFamily: 'Inter, Arial, sans-serif',
            fontWeight: 400,
            fontSize: '12px',
            lineHeight: '1.5'
          }}
        >
          By creating an account, you agree to the Terms of Service and acknowledge that you have read and understood the Privacy Policy
        </p>
      </div>
    </div>
  );
}

export default function AuthPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><Spinner /></div>}>
      <AuthPageContent />
    </Suspense>
  );
}
