import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type {
  SessionUser,
  Config,
  Model,
  Chat,
  UserSettings,
  ThemeMode,
  Banner,
  Notification
} from '@/lib/types';
import type { ChatHistory } from '@/lib/utils/chat';
import { convertMessagesToHistory } from '@/lib/utils/chat';

// Enhanced conversation entry that combines Chat data with ChatHistory structure
interface ConversationEntry {
  id: string;
  chatData: Chat;
  history: ChatHistory;
  metadata?: {
    lastUpdated?: number;
    messageCount?: number;
  };
}
import { STORAGE_KEYS, DEFAULT_SETTINGS } from '@/lib/constants';
import { WebSocketClient, getWebSocketClient } from '@/lib/websocket/client';
import type { Socket } from 'socket.io-client';

// Auth Store
interface AuthState {
  user: SessionUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  modelsLoading: boolean; // 专门用于模型加载状态
  error: string | null;
}

interface AuthActions {
  setUser: (user: SessionUser | null) => void;
  setToken: (token: string | null) => void;
  login: (user: SessionUser, token: string) => void;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  setModelsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    immer((set) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      modelsLoading: false,
      error: null,

      // Actions
      setUser: (user) => set((state) => {
        // 临时修改：强制设置为admin角色进行测试
        if (user) {
          user.role = 'admin';
        }
        state.user = user;
        state.isAuthenticated = !!user;
      }),

      setToken: (token) => set((state) => {
        state.token = token;
        // If we have a token, we should be considered authenticated
        // This prevents unnecessary redirects during token validation
        if (token) {
          state.isAuthenticated = true;
        }
      }),

      login: (user, token) => {
        set((state) => {
          // 临时修改：强制设置为admin角色进行测试
          if (user) {
            user.role = 'admin';
          }
          state.user = user;
          state.token = token;
          state.isAuthenticated = true;
          state.error = null;
          state.modelsLoading = true; // 开始加载模型
        });

        // 登录成功后立即刷新模型列表
        const appStore = useAppStore.getState();
        appStore.refreshModels(token).then(() => {
          
          // 自动选择最新的模型
          const currentAppStore = useAppStore.getState();
          const models = currentAppStore.models;
          
          if (models && models.length > 0) {
            // 选择最新的模型（数组的第一个）
            const latestModel = models[0];
            currentAppStore.setSelectedModels([latestModel.id]);
          }
          
          set((state) => {
            state.modelsLoading = false; // 模型加载完成
          });
        }).catch(error => {
          console.error('Failed to refresh models after login:', error);
          set((state) => {
            state.modelsLoading = false; // 即使出错也停止加载状态
            state.error = `Failed to load models: ${error instanceof Error ? error.message : 'Unknown error'}`;
          });
        });
      },

      logout: () => set((state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
      }),

      setLoading: (loading) => set((state) => {
        state.isLoading = loading;
      }),

      setModelsLoading: (loading) => set((state) => {
        state.modelsLoading = loading;
      }),

      setError: (error) => set((state) => {
        state.error = error;
      }),

      clearError: () => set((state) => {
        state.error = null;
      }),
    })),
    {
      name: STORAGE_KEYS.USER,
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// App Store
interface AppState {
  config: Config | null;
  models: Model[];
  banners: Banner[];
  selectedModels: string[]; // Selected models for chat
  conversations: ConversationEntry[]; // Unified conversation history using original project structure
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  // WebSocket state
  socket: Socket | null;
  socketClient: WebSocketClient | null;
  isSocketConnected: boolean;
  activeUserIds: string[];
  usagePool: string[];
  socketError: string | null;
}

interface AppActions {
  setConfig: (config: Config) => void;
  setModels: (models: Model[]) => void;
  setBanners: (banners: Banner[]) => void;
  setSelectedModels: (models: string[]) => void;
  setInitialized: (initialized: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  initialize: () => Promise<void>;
  refreshModels: (token: string) => Promise<void>;
  // Conversation management
  addConversation: (conversation: ConversationEntry) => void;
  updateConversation: (id: string, updates: Partial<ConversationEntry>) => void;
  deleteConversation: (id: string) => void;
  getConversation: (id: string) => ConversationEntry | undefined;
  // Chat history management helpers
  createConversationFromChat: (chat: Chat) => ConversationEntry;
  updateConversationHistory: (id: string, history: ChatHistory) => void;
  getConversationHistory: (id: string) => ChatHistory | null;
  // WebSocket actions
  initializeSocket: (token: string, enableWebsocket?: boolean) => Promise<void>;
  disconnectSocket: () => void;
  setSocketConnected: (connected: boolean) => void;
  setActiveUserIds: (userIds: string[]) => void;
  setUsagePool: (models: string[]) => void;
  setSocketError: (error: string | null) => void;
  emitSocketEvent: (event: string, data: any) => void;
}

export const useAppStore = create<AppState & AppActions>()(
  immer((set, get) => ({
    // State
    config: null,
    models: [],
    banners: [],
    selectedModels: [], // Default empty selection
    conversations: [], // Unified conversation history
    isInitialized: false,
    isLoading: false,
    error: null,
    // WebSocket state
    socket: null,
    socketClient: null,
    isSocketConnected: false,
    activeUserIds: [],
    usagePool: [],
    socketError: null,

    // Actions
    setConfig: (config) => set((state) => {
      state.config = config;
    }),

    setModels: (models) => set((state) => {
      state.models = models;
    }),

    setSelectedModels: (models) => set((state) => {
      state.selectedModels = models;
    }),

    setBanners: (banners) => set((state) => {
      state.banners = banners;
    }),

    setInitialized: (initialized) => set((state) => {
      state.isInitialized = initialized;
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),

    setError: (error) => set((state) => {
      state.error = error;
    }),

    initialize: async () => {
      // Prevent multiple simultaneous initializations with a more robust check
      const currentState = get();
      if (currentState.isLoading || currentState.isInitialized) {
        return;
      }

      // Double-check after a small delay to handle race conditions
      await new Promise(resolve => setTimeout(resolve, 10));
      const stateAfterDelay = get();
      if (stateAfterDelay.isLoading || stateAfterDelay.isInitialized) {
        return;
      }

      set((state) => {
        state.isLoading = true;
        state.error = null;
      });

      try {
        // Load backend configuration and models only if authenticated
        const { getBackendConfig, getModels } = await import('@/lib/api');
        const { token, isAuthenticated } = useAuthStore.getState();

        // Initialize with default values
        let backendConfig: any = {
          features: {
            enable_signup: true,
            enable_ldap: false
          }
        };
        let models: any[] = [];

        // Load backend config regardless of auth status (it's public)
        try {
          backendConfig = await getBackendConfig();
        } catch (error) {
          console.warn('Failed to load backend config, using defaults:', error);
        }

        // Try to load models (will use mock data if not authenticated)
        try {
          models = await getModels(token || '');
        } catch (error) {
          console.warn('Failed to load models, using defaults:', error);
          // Use mock models as fallback
          models = [
            {
              id: 'llama2:latest',
              name: 'Llama 2 Latest',
              owned_by: 'ollama',
              info: {
                meta: {
                  description: 'Llama 2 7B model from Ollama',
                  parameter_size: '7B',
                  capabilities: ['text-generation']
                }
              }
            }
          ];
        }

        set((state) => {
          state.config = backendConfig;
          state.models = models || [];
          // Set default model if available
          if (models && models.length > 0 && (state.selectedModels.length === 0 || (state.selectedModels.length === 1 && state.selectedModels[0] === ''))) {
            const defaultModel = models.find(m => m.id === 'MBZUAI-IFM/K2-Think-GAMMA') || models[0];
            state.selectedModels = [defaultModel.id];
          }
          state.isInitialized = true;
          state.isLoading = false;
        });
      } catch (error) {
        console.error('Failed to initialize app:', error);
        set((state) => {
          // Set defaults even on error to allow app to function
          state.config = {
            features: {
              enable_signup: true,
              enable_ldap: false
            }
          } as any;
          state.models = [];
          state.error = error instanceof Error ? error.message : 'Failed to load configuration';
          state.isInitialized = true; // Still mark as initialized to avoid blocking the app
          state.isLoading = false;
        });
      }
    },

    refreshModels: async (token: string) => {
      // Prevent duplicate model refresh calls
      const currentState = get();
      if (currentState.isLoading) {
        return;
      }

      try {
        const { getModels } = await import('@/lib/api');

        const models = await getModels(token);

        set((state) => {
          state.models = models || [];
        });
      } catch (error) {
        console.error('Failed to refresh models:', error);
        // Don't throw error, just log it - keep existing models
        set((state) => {
          state.error = `Failed to refresh models: ${error instanceof Error ? error.message : 'Unknown error'}`;
        });
      }
    },

    // Conversation management
    addConversation: (conversation) => set((state) => {
      state.conversations.unshift(conversation); // Add to beginning for newest first
    }),

    updateConversation: (id, updates) => set((state) => {
      const index = state.conversations.findIndex(conv => conv.id === id);
      if (index !== -1) {
        state.conversations[index] = { 
          ...state.conversations[index], 
          ...updates,
          metadata: {
            ...state.conversations[index].metadata,
            lastUpdated: Date.now()
          }
        };
      }
    }),

    deleteConversation: (id) => set((state) => {
      state.conversations = state.conversations.filter(conv => conv.id !== id);
    }),

    getConversation: (id) => {
      return get().conversations.find(conv => conv.id === id);
    },

    // Chat history management helpers
    createConversationFromChat: (chat) => {
      const messages = chat.messages || [];
      const history = convertMessagesToHistory(messages);
      
      return {
        id: chat.id,
        chatData: chat,
        history,
        metadata: {
          lastUpdated: Date.now(),
          messageCount: messages.length
        }
      };
    },

    updateConversationHistory: (id, history) => set((state) => {
      const index = state.conversations.findIndex(conv => conv.id === id);
      if (index !== -1) {
        state.conversations[index].history = history;
        state.conversations[index].metadata = {
          ...state.conversations[index].metadata,
          lastUpdated: Date.now(),
          messageCount: Object.keys(history.messages).length
        };
      }
    }),

    getConversationHistory: (id) => {
      const conversation = get().conversations.find(conv => conv.id === id);
      return conversation ? conversation.history : null;
    },

    // WebSocket actions
    initializeSocket: async (token: string, enableWebsocket: boolean = true) => {
      try {
        const client = getWebSocketClient();
        client.config.enableWebsocket = enableWebsocket;

        // Set up event handlers
        client.setEventHandlers({
          onConnect: () => {
            set((state) => {
              state.isSocketConnected = true;
              state.socketError = null;
            });
          },
          onDisconnect: (reason) => {
            set((state) => {
              state.isSocketConnected = false;
              state.socketError = `Disconnected: ${reason}`;
            });
          },
          onConnectError: (error) => {
            set((state) => {
              state.isSocketConnected = false;
              state.socketError = error.message;
            });
          },
          onUserList: (data) => {
            set((state) => {
              state.activeUserIds = data.user_ids || [];
            });
          },
          onUsage: (data) => {
            set((state) => {
              state.usagePool = data.models || [];
            });
          },
          onChatEvents: async (data) => {
            // Handle chat events like title updates and content streaming
            
            if (data?.data?.type === 'chat:title') {
              try {
                // Import getChatList API dynamically to avoid circular dependencies
                const { getChatList } = await import('@/lib/api/chats');
                const chatList = await getChatList(token, 1);
                
                // Log current store state before update
                const currentChats = useChatStore.getState().chats;
                
                // Directly call useChatStore to update the chat list
                useChatStore.getState().setChats(chatList.data || []);
                
                // Log store state after update
                const updatedChats = useChatStore.getState().chats;
              } catch (error) {
                console.error('Failed to refresh chat list after title update:', error);
              }
            } else if (data?.data?.type === 'chat:completion' && data?.data?.data?.content) {
              
              // Broadcast this event to any active Chat components
              // We'll use a custom event to communicate with Chat components
              const customEvent = new CustomEvent('websocket-chat-content', {
                detail: {
                  type: 'chat:completion',
                  chat_id: data.chat_id,
                  message_id: data.message_id,
                  data: {
                    content: data.data.data.content,
                    done: data.data.data.done
                  }
                }
              });
              window.dispatchEvent(customEvent);
              
            } else {
            }
          },
        });

        const socket = await client.connect(token);

        set((state) => {
          state.socketClient = client;
          state.socket = socket as any; // Type assertion to avoid readonly issues
          state.isSocketConnected = true;
          state.socketError = null;
        });
      } catch (error) {
        console.error('Failed to initialize WebSocket:', error);
        set((state) => {
          state.socketError = error instanceof Error ? error.message : 'Unknown error';
          state.isSocketConnected = false;
        });
      }
    },

    disconnectSocket: () => {
      const { socketClient } = get();
      if (socketClient) {
        socketClient.disconnect();
      }
      set((state) => {
        state.socket = null;
        state.socketClient = null;
        state.isSocketConnected = false;
        state.activeUserIds = [];
        state.usagePool = [];
        state.socketError = null;
      });
    },

    setSocketConnected: (connected: boolean) => {
      set((state) => {
        state.isSocketConnected = connected;
      });
    },

    setActiveUserIds: (userIds: string[]) => {
      set((state) => {
        state.activeUserIds = userIds;
      });
    },

    setUsagePool: (models: string[]) => {
      set((state) => {
        state.usagePool = models;
      });
    },

    setSocketError: (error: string | null) => {
      set((state) => {
        state.socketError = error;
      });
    },

    emitSocketEvent: (event: string, data: any) => {
      const { socketClient } = get();
      if (socketClient && socketClient.isConnected()) {
        socketClient.emit(event, data);
      } else {
        console.warn('WebSocket not connected, cannot emit event:', event);
      }
    },
  }))
);

// Chat Store
interface ChatState {
  chats: Chat[];
  currentChat: Chat | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean; // Track if chats have been loaded
}

interface ChatActions {
  setChats: (chats: Chat[]) => void;
  addChat: (chat: Chat) => void;
  updateChat: (chatId: string, updates: Partial<Chat>) => void;
  deleteChat: (chatId: string) => void;
  setCurrentChat: (chat: Chat | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setInitialized: (initialized: boolean) => void;
}

export const useChatStore = create<ChatState & ChatActions>()(
  persist(
    immer((set) => ({
      // State
      chats: [],
      currentChat: null,
      isLoading: false,
      error: null,
      isInitialized: false,

      // Actions
      setChats: (chats) => set((state) => {
        state.chats = chats;
      }),

      addChat: (chat) => set((state) => {
        state.chats.unshift(chat);
      }),

      updateChat: (chatId, updates) => set((state) => {
        const index = state.chats.findIndex(chat => chat.id === chatId);
        if (index !== -1) {
          state.chats[index] = { ...state.chats[index], ...updates };
        }
        if (state.currentChat?.id === chatId) {
          state.currentChat = { ...state.currentChat, ...updates };
        }
      }),

      deleteChat: (chatId) => set((state) => {
        state.chats = state.chats.filter(chat => chat.id !== chatId);
        if (state.currentChat?.id === chatId) {
          state.currentChat = null;
        }
      }),

      setCurrentChat: (chat) => set((state) => {
        state.currentChat = chat;
      }),

      setLoading: (loading) => set((state) => {
        state.isLoading = loading;
      }),

      setError: (error) => set((state) => {
        state.error = error;
      }),

      setInitialized: (initialized) => set((state) => {
        state.isInitialized = initialized;
      }),
    })),
    {
      name: STORAGE_KEYS.CHATS,
      partialize: (state) => ({
        chats: state.chats,
      }),
    }
  )
);

// Settings Store
interface SettingsState {
  settings: UserSettings;
  theme: ThemeMode;
}

interface SettingsActions {
  setSettings: (settings: UserSettings) => void;
  updateSettings: (updates: Partial<UserSettings>) => void;
  setTheme: (theme: ThemeMode) => void;
  resetSettings: () => void;
}

export const useSettingsStore = create<SettingsState & SettingsActions>()(
  persist(
    immer((set) => ({
      // State
      settings: DEFAULT_SETTINGS as any,
      theme: 'system' as ThemeMode,

      // Actions
      setSettings: (settings) => set((state) => {
        state.settings = settings;
      }),

      updateSettings: (updates) => set((state) => {
        state.settings = { ...state.settings, ...updates };
      }),

      setTheme: (theme) => set((state) => {
        state.theme = theme;
      }),

      resetSettings: () => set((state) => {
        state.settings = DEFAULT_SETTINGS as any;
        state.theme = 'system';
      }),
    })),
    {
      name: STORAGE_KEYS.SETTINGS,
    }
  )
);

// UI Store
interface UIState {
  sidebarOpen: boolean;
  settingsOpen: boolean;
  searchOpen: boolean;
  notifications: Notification[];
  isLoading: boolean;
}

interface UIActions {
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setSettingsOpen: (open: boolean) => void;
  toggleSettings: () => void;
  setSearchOpen: (open: boolean) => void;
  toggleSearch: () => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  setLoading: (loading: boolean) => void;
}

export const useUIStore = create<UIState & UIActions>()(
  immer((set) => ({
    // State
    sidebarOpen: false,
    settingsOpen: false,
    searchOpen: false,
    notifications: [],
    isLoading: false,

    // Actions
    setSidebarOpen: (open) => set((state) => {
      state.sidebarOpen = open;
    }),

    toggleSidebar: () => set((state) => {
      state.sidebarOpen = !state.sidebarOpen;
    }),

    setSettingsOpen: (open) => set((state) => {
      state.settingsOpen = open;
    }),

    toggleSettings: () => set((state) => {
      state.settingsOpen = !state.settingsOpen;
    }),

    setSearchOpen: (open) => set((state) => {
      state.searchOpen = open;
    }),

    toggleSearch: () => set((state) => {
      state.searchOpen = !state.searchOpen;
    }),

    addNotification: (notification) => set((state) => {
      state.notifications.push(notification);
    }),

    removeNotification: (id) => set((state) => {
      state.notifications = state.notifications.filter(n => n.id !== id);
    }),

    clearNotifications: () => set((state) => {
      state.notifications = [];
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),
  }))
);
