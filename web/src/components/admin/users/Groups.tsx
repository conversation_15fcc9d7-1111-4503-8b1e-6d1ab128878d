'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  Search,
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Settings,
  Shield,
  User,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Group {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  user_count: number;
  created_at: number;
}

interface Permission {
  workspace: {
    models: boolean;
    knowledge: boolean;
    prompts: boolean;
    tools: boolean;
  };
  sharing: {
    public_models: boolean;
    public_knowledge: boolean;
    public_prompts: boolean;
    public_tools: boolean;
  };
  chat: {
    controls: boolean;
    system_prompt: boolean;
    file_upload: boolean;
    delete: boolean;
    edit: boolean;
    share: boolean;
    export: boolean;
    stt: boolean;
    tts: boolean;
    call: boolean;
    multiple_models: boolean;
    temporary: boolean;
    temporary_enforced: boolean;
  };
  features: {
    direct_tool_servers: boolean;
    web_search: boolean;
    image_generation: boolean;
    code_interpreter: boolean;
    notes: boolean;
  };
}

export default function Groups() {
  const { token } = useAuthStore();
  const [groups, setGroups] = useState<Group[]>([]);
  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [showDefaultPermissionsModal, setShowDefaultPermissionsModal] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [defaultPermissions, setDefaultPermissions] = useState<Permission>({
    workspace: {
      models: false,
      knowledge: false,
      prompts: false,
      tools: false
    },
    sharing: {
      public_models: false,
      public_knowledge: false,
      public_prompts: false,
      public_tools: false
    },
    chat: {
      controls: true,
      system_prompt: true,
      file_upload: true,
      delete: true,
      edit: true,
      share: true,
      export: true,
      stt: true,
      tts: true,
      call: true,
      multiple_models: true,
      temporary: true,
      temporary_enforced: false
    },
    features: {
      direct_tool_servers: false,
      web_search: true,
      image_generation: true,
      code_interpreter: true,
      notes: true
    }
  });

  // Mock API functions - replace with actual API calls
  const getGroups = async () => {
    try {
      setIsLoading(true);
      // Mock groups data
      const mockGroups: Group[] = [
        {
          id: '1',
          name: 'Administrators',
          description: 'Full system access with all permissions',
          permissions: ['admin', 'read', 'write', 'delete', 'manage_users'],
          user_count: 2,
          created_at: Date.now() - 86400000
        },
        {
          id: '2',
          name: 'Standard Users',
          description: 'Regular chat access with basic permissions',
          permissions: ['read', 'write', 'chat'],
          user_count: 45,
          created_at: Date.now() - 172800000
        },
        {
          id: '3',
          name: 'Premium Users',
          description: 'Enhanced features access',
          permissions: ['read', 'write', 'chat', 'advanced_features'],
          user_count: 12,
          created_at: Date.now() - 259200000
        },
        {
          id: '4',
          name: 'Content Creators',
          description: 'Special permissions for content creation',
          permissions: ['read', 'write', 'chat', 'create_content', 'share_public'],
          user_count: 8,
          created_at: Date.now() - 345600000
        }
      ];
      
      setGroups(mockGroups);
    } catch (error) {
      console.error('Failed to load groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createNewGroup = async (group: Partial<Group>) => {
    try {
      // Mock create group - replace with actual API call
      const newGroup: Group = {
        id: String(Date.now()),
        name: group.name || '',
        description: group.description || '',
        permissions: group.permissions || [],
        user_count: 0,
        created_at: Date.now()
      };
      setGroups([...groups, newGroup]);
    } catch (error) {
      console.error('Failed to create group:', error);
    }
  };

  const deleteGroup = async (id: string) => {
    try {
      // Mock delete - replace with actual API call
      setGroups(groups.filter(g => g.id !== id));
    } catch (error) {
      console.error('Failed to delete group:', error);
    }
  };

  useEffect(() => {
    getGroups();
  }, []);

  const filteredGroups = groups.filter((group) => {
    if (search === '') {
      return true;
    } else {
      const name = group.name.toLowerCase();
      const query = search.toLowerCase();
      return name.includes(query);
    }
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getPermissionBadgeColor = (permission: string) => {
    const colors: Record<string, string> = {
      admin: 'bg-red-100 text-red-800',
      read: 'bg-blue-100 text-blue-800',
      write: 'bg-green-100 text-green-800',
      delete: 'bg-red-100 text-red-800',
      chat: 'bg-purple-100 text-purple-800',
      advanced_features: 'bg-yellow-100 text-yellow-800',
      create_content: 'bg-indigo-100 text-indigo-800',
      share_public: 'bg-pink-100 text-pink-800',
      manage_users: 'bg-orange-100 text-orange-800'
    };
    return colors[permission] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex-1">
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search groups..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowDefaultPermissionsModal(true)}
            >
              <Settings className="w-4 h-4 mr-2" />
              Default Permissions
            </Button>
          </div>
        </div>

        <Button onClick={() => setShowCreateGroupModal(true)} className="ml-4">
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full text-center py-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              <span className="ml-2">Loading groups...</span>
            </div>
          </div>
        ) : filteredGroups.length === 0 ? (
          <div className="col-span-full text-center py-8 text-gray-500">
            {search ? 'No groups found matching your search' : 'No groups created yet'}
          </div>
        ) : (
          filteredGroups.map((group) => (
            <Card key={group.id} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                      {group.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {group.description}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedGroup(group)}
                >
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* User Count */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Members
                    </span>
                  </div>
                  <Badge variant="secondary">{group.user_count}</Badge>
                </div>

                {/* Permissions */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Permissions
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {group.permissions.slice(0, 3).map((permission) => (
                      <Badge
                        key={permission}
                        variant="outline"
                        className={cn("text-xs", getPermissionBadgeColor(permission))}
                      >
                        {permission.replace('_', ' ')}
                      </Badge>
                    ))}
                    {group.permissions.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{group.permissions.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500">
                  Created {formatDate(group.created_at)}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => setSelectedGroup(group)}
                >
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                  onClick={() => deleteGroup(group.id)}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                >
                  <ChevronRight className="w-3 h-3" />
                </Button>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* TODO: Add modals for Create Group, Edit Group, Default Permissions */}
      {/* These would be separate components similar to the original Svelte implementation */}
    </div>
  );
}