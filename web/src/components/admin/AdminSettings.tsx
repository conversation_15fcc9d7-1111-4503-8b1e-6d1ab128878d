'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuthStore } from '@/stores/auth';
import { toast } from 'sonner';
import {
  Settings,
  Users,
  Database,
  Zap,
  Image,
  Volume2,
  Globe,
  Code,
  BarChart,
  Shield,
  Palette,
  Search,
  FileText,
  Bot,
  Wrench
} from 'lucide-react';

// Import individual setting components
import { GeneralSettings } from './settings/GeneralSettings';
import { ModelsSettings } from './settings/ModelsSettings';
import { UsersSettings } from './settings/UsersSettings';
import { InterfaceSettings } from './settings/InterfaceSettings';
import { AudioSettings } from './settings/AudioSettings';
import { ImagesSettings } from './settings/ImagesSettings';
import { ConnectionsSettings } from './settings/ConnectionsSettings';
import { DocumentsSettings } from './settings/DocumentsSettings';
import { WebSearchSettings } from './settings/WebSearchSettings';
import { PipelinesSettings } from './settings/PipelinesSettings';
import { AnalyticsSettings } from './settings/AnalyticsSettings';
import { DatabaseSettings } from './settings/DatabaseSettings';
import { CodeExecutionSettings } from './settings/CodeExecutionSettings';
import { ToolsSettings } from './settings/ToolsSettings';

interface SettingsTab {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
  adminOnly?: boolean;
}

const settingsTabs: SettingsTab[] = [
  {
    id: 'general',
    label: 'General',
    icon: <Settings className="h-4 w-4" />,
    component: GeneralSettings
  },
  {
    id: 'models',
    label: 'Models',
    icon: <Bot className="h-4 w-4" />,
    component: ModelsSettings
  },
  {
    id: 'users',
    label: 'Users',
    icon: <Users className="h-4 w-4" />,
    component: UsersSettings,
    adminOnly: true
  },
  {
    id: 'interface',
    label: 'Interface',
    icon: <Palette className="h-4 w-4" />,
    component: InterfaceSettings
  },
  {
    id: 'audio',
    label: 'Audio',
    icon: <Volume2 className="h-4 w-4" />,
    component: AudioSettings
  },
  {
    id: 'images',
    label: 'Images',
    icon: <Image className="h-4 w-4" />,
    component: ImagesSettings
  },
  {
    id: 'connections',
    label: 'Connections',
    icon: <Globe className="h-4 w-4" />,
    component: ConnectionsSettings
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: <FileText className="h-4 w-4" />,
    component: DocumentsSettings
  },
  {
    id: 'websearch',
    label: 'Web Search',
    icon: <Search className="h-4 w-4" />,
    component: WebSearchSettings
  },
  {
    id: 'pipelines',
    label: 'Pipelines',
    icon: <Zap className="h-4 w-4" />,
    component: PipelinesSettings
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <BarChart className="h-4 w-4" />,
    component: AnalyticsSettings,
    adminOnly: true
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: <Wrench className="h-4 w-4" />,
    component: ToolsSettings,
    adminOnly: true
  },
  {
    id: 'code-execution',
    label: 'Code Execution',
    icon: <Code className="h-4 w-4" />,
    component: CodeExecutionSettings,
    adminOnly: true
  },
  {
    id: 'database',
    label: 'Database',
    icon: <Database className="h-4 w-4" />,
    component: DatabaseSettings,
    adminOnly: true
  }
];

interface AdminSettingsProps {
  className?: string;
  defaultTab?: string;
}

export const AdminSettings: React.FC<AdminSettingsProps> = ({
  className,
  defaultTab = 'general'
}) => {
  const { user } = useAuthStore();
  const [selectedTab, setSelectedTab] = useState(defaultTab);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const isAdmin = true; // 临时测试：强制显示admin权限

  // Filter tabs based on user role
  const availableTabs = settingsTabs.filter(tab => 
    !tab.adminOnly || isAdmin
  );

  // Check if user has permission to access admin settings
  useEffect(() => {
    if (!isAdmin) {
      toast.error('You do not have permission to access admin settings');
      return;
    }
  }, [isAdmin]);

  const handleTabChange = (newTab: string) => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this tab?'
      );
      if (!confirmed) return;
    }
    
    setSelectedTab(newTab);
    setHasUnsavedChanges(false);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Save logic would be implemented here
      // This would typically call an API to save the settings
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      setHasUnsavedChanges(false);
      toast.success('Settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings');
      console.error('Save error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    const confirmed = window.confirm(
      'Are you sure you want to reset all settings to their default values?'
    );
    
    if (confirmed) {
      setHasUnsavedChanges(false);
      toast.success('Settings reset to defaults');
    }
  };

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Access Denied
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            You need administrator privileges to access these settings.
          </p>
        </div>
      </div>
    );
  }

  const selectedTabData = availableTabs.find(tab => tab.id === selectedTab);
  const SelectedComponent = selectedTabData?.component;

  return (
    <div className={cn("flex flex-col h-full bg-white dark:bg-gray-900", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Admin Settings
          </h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Configure system-wide settings and preferences
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {hasUnsavedChanges && (
            <span className="text-sm text-amber-600 dark:text-amber-400">
              Unsaved changes
            </span>
          )}
          
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
          >
            Reset to Defaults
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <ScrollArea className="h-full p-4">
            <div className="space-y-1">
              {availableTabs.map((tab) => (
                <Button
                  key={tab.id}
                  variant={selectedTab === tab.id ? 'default' : 'ghost'}
                  onClick={() => handleTabChange(tab.id)}
                  className="w-full justify-start gap-3"
                >
                  {tab.icon}
                  {tab.label}
                </Button>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-6">
              {SelectedComponent ? (
                <SelectedComponent
                  onSettingsChange={() => setHasUnsavedChanges(true)}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    Settings panel not found
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};
