'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/stores';
import { useUIStore } from '@/lib/stores';
import { Button } from '@/components/ui/button';
import { SettingsModal } from '@/components/settings/SettingsModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  User,
  Settings,
  LogOut,
  Archive,
  Code,
  BarChart,
  Users,
  Shield,
  ChevronDown
} from 'lucide-react';

interface UserMenuProps {
  className?: string;
  onOpenSettings?: () => void;
  onOpenArchivedChats?: () => void;
}

export const UserMenu: React.FC<UserMenuProps> = ({
  className,
  onOpenSettings,
  onOpenArchivedChats
}) => {
  const router = useRouter();
  const { user } = useAuthStore();
  const { sidebarOpen, setSidebarOpen } = useUIStore();
  const [activeUsers, setActiveUsers] = useState<number>(0); // This would come from a real-time store
  const [showSettings, setShowSettings] = useState(false);

  const isAdmin = true; // 临时测试：强制显示admin选项

  const handleSignOut = async () => {
    try {
      // Clear all auth-related data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Clear any other stored data if needed
      sessionStorage.clear();
      
      // Force page reload to clear app state
      window.location.href = '/auth';
    } catch (error) {
      console.error('Failed to sign out:', error);
      // Fallback to router navigation
      router.push('/auth');
    }
  };

  const handleOpenSettings = () => {
    if (onOpenSettings) {
      onOpenSettings();
    } else {
      // Show settings modal
      setShowSettings(true);
    }
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const handleOpenArchivedChats = () => {
    if (onOpenArchivedChats) {
      onOpenArchivedChats();
    } else {
      // Navigate to archived chats page
      router.push('/archived');
    }
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const handleNavigate = (path: string) => {
    router.push(path);
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      <div className={cn("flex items-center justify-between p-3", className)}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center gap-2 w-full justify-start px-2 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
                {user?.profile_image_url ? (
                  <img 
                    src={user.profile_image_url} 
                    alt={user.name || 'User'} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </span>
                )}
              </div>
              
              <div className="flex-1 min-w-0 text-left">
                <div className="text-sm font-medium truncate">
                  {user?.name || user?.email || 'User'}
                </div>
                {user?.role && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </div>
                )}
              </div>
              
              <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </Button>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent align="start" className="w-56 bg-white dark:bg-gray-900">
            <DropdownMenuItem 
              onClick={handleOpenSettings}
              className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            
            <DropdownMenuItem 
              onClick={handleOpenArchivedChats}
              className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            >
              <Archive className="w-4 h-4 mr-2" />
              Archived Chats
            </DropdownMenuItem>
            
            {isAdmin && (
              <>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem 
                  onClick={() => handleNavigate('/playground')}
                  className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
                >
                  <Code className="w-4 h-4 mr-2" />
                  Playground
                </DropdownMenuItem>
                
                <DropdownMenuItem 
                  onClick={() => handleNavigate('/admin')}
                  className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Admin Panel
                </DropdownMenuItem>
                
                <DropdownMenuItem 
                  onClick={() => handleNavigate('/analytics')}
                  className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
                >
                  <BarChart className="w-4 h-4 mr-2" />
                  Analytics
                </DropdownMenuItem>
              </>
            )}
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={handleSignOut} 
              className="text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </DropdownMenuItem>
            
            {activeUsers > 0 && (
              <>
                <DropdownMenuSeparator />
                
                <div className="flex items-center gap-2 px-2 py-1.5 text-xs text-gray-500 dark:text-gray-400">
                  <div className="relative flex h-2 w-2">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75" />
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500" />
                  </div>
                  <div>
                    <span>Active Users: </span>
                    <span className="font-semibold">{activeUsers}</span>
                  </div>
                </div>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Settings Modal */}
      <SettingsModal 
        show={showSettings} 
        onClose={() => setShowSettings(false)} 
      />
    </>
  );
};
