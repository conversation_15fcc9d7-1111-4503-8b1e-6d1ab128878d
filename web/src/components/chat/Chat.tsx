'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { flushSync } from 'react-dom';
import { useAuthStore, useChatStore, useUIStore, useAppStore } from '@/lib/stores';
import { useChats } from '@/hooks/useChats';
import { useWebSocket } from '@/hooks/useWebSocket';

import { Messages } from './Messages';
import { MessageInput } from './MessageInput';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createOpenAITextStream } from '@/lib/api/chat-completion';
import { WEBUI_BASE_URL } from '@/lib/constants';
import type { Message } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { 
  createMessagesList, 
  convertMessagesToHistory, 
  convertHistoryToMessages,
  type ChatHistory,
  type HistoryMessage
} from '@/lib/utils/chat';
import { chatCompleted } from '@/lib/api/tasks';
import { updateChatById, getChatList, getChatById } from '@/lib/api/chats';

interface ChatProps {
  chatId?: string;
  selectedModels?: string[];
  initialMessages?: Message[];
  onUpdate?: (updates: any) => void;
  className?: string;
}

export const Chat: React.FC<ChatProps> = ({
  chatId: propChatId,
  selectedModels = [],
  initialMessages = [],
  onUpdate,
  className
}) => {
  // Helper function to get prompt variables (like original project)
  const getPromptVariables = useCallback((userName?: string, userLocation?: string) => {
    return {
      '{{USER_NAME}}': userName || 'User',
      '{{USER_LOCATION}}': userLocation || 'Unknown',
      '{{CURRENT_DATETIME}}': new Date().toLocaleString(),
      '{{CURRENT_DATE}}': new Date().toLocaleDateString(),
      '{{CURRENT_TIME}}': new Date().toLocaleTimeString(),
      '{{CURRENT_WEEKDAY}}': new Date().toLocaleDateString('en-US', { weekday: 'long' }),
      '{{CURRENT_TIMEZONE}}': Intl.DateTimeFormat().resolvedOptions().timeZone,
      '{{USER_LANGUAGE}}': navigator.language || 'en-US'
    };
  }, []);

  // Helper function to validate chat ID format - 需要先定义这个函数
  const isValidChatId = useCallback((id: string) => {
    if (!id || id === 'new' || id === 'local') {
      return false;
    }
    // Only accept UUID format (8-4-4-4-12) for server chat IDs
    // This prevents local conversation IDs from triggering server API calls
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const isValid = uuidRegex.test(id);
    return isValid;
  }, []);

  // 简化 chatId 解析逻辑，优先使用 props 传入的 chatId（对齐原项目）
  const chatId = propChatId;

  const { token } = useAuthStore();
  const { sidebarOpen } = useUIStore();
  const { currentChat, setCurrentChat } = useChatStore();
  const { 
    getConversation, 
    addConversation, 
    updateConversation,
    createConversationFromChat,
    updateConversationHistory,
    getConversationHistory,
    socket,
    socketClient,
    models
  } = useAppStore();

  const { loadChat, createChat, createChatWithUpdate, updateChatData, loadChats, loadTags } = useChats();
  const { connect: connectWebSocket, isConnected: isWebSocketConnected } = useWebSocket();

  // Original project-style state management
  const [prompt, setPrompt] = useState('');
  const [files, setFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [autoScroll] = useState(true);
  const [processing, setProcessing] = useState('');
  const [chatNotFound, setChatNotFound] = useState(false);
  const [abortControllers, setAbortControllers] = useState<Map<string, AbortController>>(new Map());

  // Track completed messages to prevent duplicate chatCompletedHandler calls
  const completedMessagesRef = useRef<Set<string>>(new Set());
  
  // Tool and feature states (like original project)
  const [selectedToolIds, setSelectedToolIds] = useState<string[]>([]);
  const [imageGenerationEnabled, setImageGenerationEnabled] = useState(false);
  const [webSearchEnabled, setWebSearchEnabled] = useState(false);
  const [codeInterpreterEnabled, setCodeInterpreterEnabled] = useState(false);
  
  // Additional states to match original project
  const [params, setParams] = useState({});
  
  // Use history structure like original project
  const [history, setHistory] = useState<ChatHistory>({
    messages: {},
    currentId: null
  });
  
  // Track loading state for chat history to prevent showing stale content
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  
  // Track new chat creation to prevent UI flicker
  const [isCreatingNewChat, setIsCreatingNewChat] = useState(false);
  
  // Track message submission to prevent UI flicker during the entire process
  const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);
  
  // Derive messages from history (like original project)
  // Only show messages when history loading is complete to prevent showing stale content
  const messages = (isHistoryLoading || !history.currentId) ? [] : createMessagesList(history, history.currentId);
  
  // Determine if this is truly a new chat (not just loading a historical chat)
  // Include isCreatingNewChat to prevent flicker during new chat creation
  const isTrueNewChat = !isHistoryLoading && !isCreatingNewChat && !chatId && !currentChat && messages.length === 0;

  // Ref to prevent multiple simultaneous loads
  const isLoadingRef = useRef(false);

  // Load chat when chatId changes
  useEffect(() => {

    // Check current route to determine behavior
    const isServerChatRoute = typeof window !== 'undefined' && window.location.pathname.startsWith('/c/');
    const isHomePage = typeof window !== 'undefined' && window.location.pathname === '/';

    if (chatId && chatId !== 'new') {
      // Validate chatId format before attempting to load
      if (!isValidChatId(chatId)) {
        return;
      }
      
      // Always try to load from server for chat routes
      if (window.location.pathname.startsWith('/c/')) {
        // CRITICAL FIX: Always clear current state when switching to a different chat
        if (currentChat && currentChat.id !== chatId) {
          console.log('🔄 [Chat] Switching from', currentChat.id, 'to', chatId, '- clearing current state');
          setCurrentChat(null);
          setHistory({ messages: {}, currentId: null });
          setIsHistoryLoading(true);
        }
        
        // 检查当前聊天是否需要重新加载
        // 如果当前聊天的消息为空或格式不正确，需要重新加载
        const needsReload = !currentChat || 
                          currentChat.id !== chatId || 
                          !currentChat.chat?.messages || 
                          (typeof currentChat.chat.messages === 'object' && Object.keys(currentChat.chat.messages).length === 0) ||
                          (Array.isArray(currentChat.chat.messages) && currentChat.chat.messages.length === 0);
        
        if (!needsReload && currentChat?.id === chatId) {
          console.log('🔄 [Chat] Chat already loaded, no reload needed');
          return;
        }

        if (isLoadingRef.current) {
          console.log('🔄 [Chat] Already loading, skipping');
          return;
        }

        console.log('🔄 [Chat] Loading chat:', chatId);
        isLoadingRef.current = true;
        
        loadChat(chatId).then((loadedChat) => {
          console.log('🔄 [Chat] Successfully loaded chat:', loadedChat?.id);
          isLoadingRef.current = false;
        }).catch((error) => {
          console.error('Failed to load chat:', error);
          isLoadingRef.current = false;
        });
      } else if (isHomePage) {
        // For home page, don't load server chat data
        // Clear server chat state to prevent interference
        if (currentChat && isValidChatId(currentChat.id)) {
          console.log('🔄 [Chat] Home page - clearing server chat state');
          setCurrentChat(null);
        }
        // IMPORTANT: Return early to prevent server loading for local conversation IDs
        return;
      }
    } else {
      // No chatId or chatId is 'new'
      if (isHomePage && currentChat && isValidChatId(currentChat.id)) {
        // On home page without valid chatId, clear any server chat data
        console.log('🔄 [Chat] Home page without chatId - clearing server chat state');
        setCurrentChat(null);
      }
    }
  }, [chatId, loadChat, setCurrentChat, currentChat?.id]); // Include currentChat.id to detect changes

  // Helper function to validate and fix message roles if needed
  const validateAndFixMessageRoles = useCallback((messages: any[]) => {

    const fixedMessages = messages.map((msg, index) => {
      // Basic validation: alternate between user and assistant
      const expectedRole = index % 2 === 0 ? 'user' : 'assistant';
      
      // If the role doesn't match expected pattern, try to infer from content
      if (msg.role !== expectedRole) {

        
        // Try to infer role from content or other properties
        let inferredRole = msg.role; // Keep original by default
        
        // If it's clearly user content (short, question-like)
        if (msg.content && msg.content.length < 200 && !msg.model) {
          inferredRole = 'user';
        }
        // If it has model info or is a long response, it's likely assistant
        else if (msg.model || (msg.content && msg.content.length > 200)) {
          inferredRole = 'assistant';
        }
        
        if (inferredRole !== msg.role) {
          return { ...msg, role: inferredRole };
        }
      }
      
      return msg;
    });


    return fixedMessages;
  }, []);

  // Update history when current chat changes
  useEffect(() => {
    const isServerChatRoute = typeof window !== 'undefined' && window.location.pathname.startsWith('/c/');
    const isHomePage = typeof window !== 'undefined' && window.location.pathname === '/';

    // CRITICAL FIX: Always clear history first to prevent showing previous chat content
    if (isServerChatRoute) {
      // If we're on a server chat route but currentChat is not for the current chatId,
      // clear the history immediately to prevent showing wrong content
      if (!currentChat || currentChat.id !== chatId) {
        console.log('🔄 [Chat] Clearing history - waiting for correct chat to load');
        setHistory({ messages: {}, currentId: null });
        return; // Return early, don't process until we have the correct chat
      }
    }

    if (currentChat && isServerChatRoute && currentChat.id === chatId) {
      // Only process server chat data when on /c/ routes AND chat IDs match
      let chatHistory: ChatHistory = {
        messages: {},
        currentId: null
      };
      
      console.log('🔄 [Chat] Processing chat history for:', currentChat.id);
      
      if (currentChat.chat) {
        // Check if chat.chat has history structure (preferred format)
        if (currentChat.chat.history && currentChat.chat.history.messages) {
          // Use the history structure directly
          chatHistory = {
            messages: currentChat.chat.history.messages,
            currentId: currentChat.chat.history.currentId
          };
          
        } else if (currentChat.chat.messages) {
          // Convert legacy messages array to history structure
          let messagesArray;
          if (Array.isArray(currentChat.chat.messages)) {
            messagesArray = validateAndFixMessageRoles(currentChat.chat.messages);
          } else {
            // Handle messages as object - sort by timestamp to maintain order
            
            messagesArray = Object.values(currentChat.chat.messages)
              .sort((a: any, b: any) => {
                const aTime = a.timestamp || 0;
                const bTime = b.timestamp || 0;
                return aTime - bTime; // Sort chronologically
              });
              
            
            // Apply role validation and fixing
            messagesArray = validateAndFixMessageRoles(messagesArray);
          }
          
          chatHistory = convertMessagesToHistory(messagesArray as Message[]);
        }
      } else if (currentChat.messages) {
        // Fallback to direct messages array (if exists)
        chatHistory = convertMessagesToHistory(currentChat.messages);
      }
      
      console.log('🔄 [Chat] Setting new history with', Object.keys(chatHistory.messages).length, 'messages');
      setHistory(chatHistory);
      setIsHistoryLoading(false);
      
      // Update app store conversation if needed
      if (chatId && currentChat) {
        const conversation = getConversation(chatId);
        if (!conversation) {
          const newConversation = createConversationFromChat(currentChat);
          newConversation.history = chatHistory;
          addConversation(newConversation);
        } else {
          updateConversationHistory(chatId, chatHistory);
        }
      }
    } else if (isHomePage || !isServerChatRoute) {
      // Clear history for home page or non-server routes
      setHistory({ messages: {}, currentId: null });
      setIsHistoryLoading(false);
    }
    // Note: We don't update history when currentChat exists but we're on home page
    // because home page manages its own conversation state
  }, [currentChat, chatId, getConversation, createConversationFromChat, addConversation, updateConversationHistory, validateAndFixMessageRoles]);

  // Initialize WebSocket connection for receiving title updates (like original project)
  useEffect(() => {
    if (token && !isWebSocketConnected) {
      connectWebSocket().catch(error => {
        console.error('Failed to connect WebSocket:', error);
      });
    }
  }, [token, isWebSocketConnected, connectWebSocket]);



  // Connect WebSocket for real-time updates (like title updates)
  useEffect(() => {
    
    if (token && !isWebSocketConnected) {
      connectWebSocket().then(() => {
      }).catch(error => {
        console.error('Failed to connect WebSocket:', error);
      });
    }
  }, [token, isWebSocketConnected, connectWebSocket, socketClient, socket]);

  // Save user message to server before generating AI response
  const saveUserMessageToServer = useCallback(async (currentChatId: string, currentMessages: Message[]) => {
    if (!token || !isValidChatId(currentChatId)) {
      return;
    }

    try {
      // Prepare chat data with just the user message
      const chatUpdateData = {
        models: selectedModels,
        messages: currentMessages,
        history: {
          messages: currentMessages.reduce((acc, msg) => {
            acc[msg.id] = msg;
            return acc;
          }, {} as Record<string, Message>),
          currentId: currentMessages[currentMessages.length - 1]?.id
        },
        timestamp: Date.now()
      };

      await updateChatData(currentChatId, chatUpdateData);
    } catch (error) {
      console.error('Failed to save user message:', error);
      // Don't throw error, allow AI response to continue
    }
  }, [token, selectedModels, updateChatData, isValidChatId]);

  // Using HTTP API for all chat functionality

  // Submit prompt (like original project)
  const submitPrompt = useCallback(async (userPrompt: string, options: { _raw?: boolean } = {}) => {
    
    const messagesList = createMessagesList(history, history.currentId);
    
    if (userPrompt === '' && files.length === 0) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!selectedModels.length || selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    if (!token) {
      toast.error('Please sign in to continue');
      return;
    }

    // CRITICAL FIX: Set creating state immediately if this is a new chat
    // This prevents ChatPlaceholder from showing during the transition
    const isNewChat = messagesList.length === 0;
    
    // CRITICAL FIX: Set submitting state for the entire duration
    setIsSubmittingMessage(true);
    
    // Generate user message ID
    const userMessageId = uuidv4();
    
    // Create the user message in history structure
    const userMessage: HistoryMessage = {
      id: userMessageId,
      parentId: history.currentId,
      childrenIds: [],
      role: 'user',
      content: userPrompt,
      timestamp: Math.floor(Date.now() / 1000),
      files: files || [],
      done: true
    };

    // Update history with user message using deep copy to avoid extensibility issues
    const updatedHistory = {
      ...history,
      messages: { ...history.messages },
      currentId: userMessageId
    };
    
    // Add the user message
    updatedHistory.messages[userMessageId] = userMessage;
    
    // Update parent's children if exists
    if (history.currentId && updatedHistory.messages[history.currentId]) {
      updatedHistory.messages[history.currentId] = {
        ...updatedHistory.messages[history.currentId],
        childrenIds: [...updatedHistory.messages[history.currentId].childrenIds, userMessageId]
      };
    }
    
    // CRITICAL FIX: Use flushSync to ensure both state updates happen atomically
    // This prevents the intermediate render where we have messages but isCreatingNewChat is false
    flushSync(() => {
      if (isNewChat) {
        console.log('🚀 [submitPrompt] Setting isCreatingNewChat = true for new chat');
        setIsCreatingNewChat(true);
      }
      setHistory(updatedHistory);
    });

    // Clear input
    setPrompt('');
    setFiles([]);

    // Focus chat input
    const chatInput = document.getElementById('chat-input');
    chatInput?.focus();

    // 完全对齐原项目的逻辑：submitPrompt 总是传递 newChat: true
    // 真正的判断逻辑在 sendPrompt 中进行
    try {
      await sendPrompt(updatedHistory, userPrompt, userMessageId, { newChat: true });
    } catch (error) {
      // If sendPrompt fails, clear all loading states
      setIsSubmittingMessage(false);
      if (isNewChat) {
        setIsCreatingNewChat(false);
      }
      throw error;
    } finally {
      // Always clear submitting state when done
      setIsSubmittingMessage(false);
    }
  }, [history, files, selectedModels, token, chatId, isValidChatId, currentChat]);

  // Save chat handler (步骤3-4: 更新聊天内容并刷新列表) - 提前定义
  const saveChatHandler = useCallback(async (currentChatId: string, _history: ChatHistory) => {
    
    if (!currentChatId || !isValidChatId(currentChatId) || !token || !_history) {
      return;
    }

    try {
      // Update chat with new history
      const chatUpdateData = {
        models: selectedModels,
        history: _history,
        timestamp: Date.now()
      };

      await updateChatById(token, currentChatId, chatUpdateData);
      
      // Refresh chat list
      try {
        await loadChats(1);
      } catch (error) {
        console.error('Failed to refresh chat list after saving:', error);
      }
      
    } catch (error) {
      console.error('Failed to save chat:', error);
    }
  }, [selectedModels, token, isValidChatId, loadChats]);

  // Chat completed handler - 提前定义
  const chatCompletedHandler = useCallback(async (
    currentChatId: string,
    modelId: string,
    responseMessageId: string,
    _history: ChatHistory,
    isFirstChat: boolean = false,
    originalCurrentId: string | null = null
  ) => {
    if (!currentChatId || !isValidChatId(currentChatId) || !token || !_history) {
      return;
    }

    try {
      // 调用 chatCompleted API (/api/chat/completed)
      const messagesList = createMessagesList(_history, responseMessageId);
      const sessionId = socket?.id || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      
      const chatCompletedData = {
        model: modelId,
        messages: messagesList.map((m) => {
          return {
            id: m.id,
            role: m.role,
            content: m.content,
            info: m.info ? m.info : undefined,
            timestamp: m.timestamp,
            ...(m.usage && { usage: m.usage }),
            ...(m.sources && { sources: m.sources })
          };
        }),
        model_item: models.find((m) => m.id === modelId),
        chat_id: currentChatId,
        session_id: sessionId,
        id: originalCurrentId || _history.currentId // Use original currentId from API, fallback to current
      };
      
      
      const res = await chatCompleted(token, chatCompletedData);
      
      
      
      // 处理返回的消息（如果有）
      if (res !== null && res.messages) {
        // Update chat history with the new messages (对齐原项目逻辑)
        for (const message of res.messages) {
          if (message?.id) {
            _history.messages[message.id] = {
              ..._history.messages[message.id],
              ...(_history.messages[message.id].content !== message.content
                ? { originalContent: _history.messages[message.id].content }
                : {}),
              ...message
            };
          }
        }
      }
      
      if (isFirstChat) {
        // Update chat for first chat
        const chatUpdateData = {
          models: selectedModels,
          history: _history,
          timestamp: Date.now()
        };
        
        await updateChatById(token, currentChatId, chatUpdateData);
        

        
        try {
          await loadChats(1);
        } catch (error) {
          console.error('Failed to refresh chat list after chat completed:', error);
        }
        
        try {
          await loadTags();
        } catch (error) {
          console.error('Failed to load tags after chat completed:', error);
        }
      } else {
        // 非第一次聊天的步骤5-7
        try {
          await loadChats(1);
        } catch (error) {
          console.error('Failed to refresh chat list after chat completed:', error);
        }
        
        // Update chat for subsequent chats
        const chatUpdateData = {
          models: selectedModels,
          history: _history,
          timestamp: Date.now()
        };
        
        await updateChatById(token, currentChatId, chatUpdateData);
        
        try {
          await loadChats(1);
        } catch (error) {
          console.error('Failed to refresh chat list final:', error);
        }
      }
      
    } catch (error) {
      console.error('Failed to handle chat completion:', error);
    }
  }, [selectedModels, token, isValidChatId, socket, models, loadChats, loadTags]);

  // Send prompt (like original project)
  const sendPrompt = useCallback(async (
    _history: ChatHistory,
    prompt: string,
    parentId: string,
    options: { modelId?: string | null; modelIdx?: number | null; newChat?: boolean } = {}
  ) => {
    const { modelId, modelIdx, newChat = false } = options;

    setIsLoading(true);
    setProcessing('');

    let currentChatId = chatId;
    
    // 从URL路由中提取真正的chatId（UUID格式）
    if (typeof window !== 'undefined' && window.location.pathname.startsWith('/c/')) {
      const routeChatId = window.location.pathname.split('/c/')[1];
      if (routeChatId && routeChatId !== chatId) {
        currentChatId = routeChatId;
      }
    }
    
    // 如果有 currentChat，也检查其真正的 ID（UUID格式）
    if (currentChat?.id && currentChat.id !== chatId && currentChat.id !== currentChatId) {
      currentChatId = currentChat.id;
    }
    

    // 完全对齐原项目逻辑：只有当 newChat 为 true 且当前消息是第一条消息时才创建新聊天
    // 原项目代码：if (newChat && _history.messages[_history.currentId].parentId === null)
    if (newChat && _history.messages[_history.currentId].parentId === null) {
      currentChatId = await initChatHandler(_history);
      
      // 第一次聊天：步骤3-4 在 initChatHandler 之后调用 saveChatHandler
      if (currentChatId && isValidChatId(currentChatId)) {
        await saveChatHandler(currentChatId, _history);
      }
    } else {
      // 非第一次聊天：直接调用步骤1-2的逻辑（updateChatById + getChatList）
      if (currentChatId && isValidChatId(currentChatId)) {
        await saveChatHandler(currentChatId, _history);
      } else {
      }
    }

    // Process each selected model (original project supports multiple models)
    const modelPromises = selectedModels.map(async (selectedModelId, modelIndex) => {
      const responseMessageId = uuidv4();
      
      // Find the model
      const model = { id: selectedModelId, name: selectedModelId }; // Simplified model structure
      
      if (model) {
        // Create assistant message placeholder (like original project)
        const responseMessage: HistoryMessage = {
          id: responseMessageId,
          parentId: parentId,
          childrenIds: [],
          role: 'assistant',
          content: '',
          timestamp: Math.floor(Date.now() / 1000),
          model: model.id,
          modelName: model.name,
          modelIdx: modelIndex,
          done: false,
          sources: [],
          files: []
        };

        // Store original currentId before updating (for API calls)
        const originalCurrentId = _history.currentId;
        
        // Update history with response message using deep copy to avoid extensibility issues
        const updatedHistory = {
          ..._history,
          messages: { ..._history.messages }
        };
        updatedHistory.messages[responseMessageId] = responseMessage;
        updatedHistory.messages[parentId] = {
          ...updatedHistory.messages[parentId],
          childrenIds: [...updatedHistory.messages[parentId].childrenIds, responseMessageId]
        };
        updatedHistory.currentId = responseMessageId;
        
        setHistory(updatedHistory);

        // Generate response using the model
        // 判断是否是第一次聊天：检查 parentId 对应的用户消息是否为第一条消息（parentId 为 null）
        const contextUserMessage = updatedHistory.messages[parentId];
        const isFirstChat = contextUserMessage?.parentId === null;
        await sendPromptSocket(updatedHistory, model, responseMessageId, currentChatId, isFirstChat, originalCurrentId);
      } else {
        toast.error(`Model ${selectedModelId} not found`);
      }
    });

    // Wait for all model responses (步骤5开始)
    await Promise.all(modelPromises);
    
    // Loading state will be managed by individual sendPromptSocket calls
  }, [selectedModels, chatId, isValidChatId, saveChatHandler]);

  // Initialize chat handler (like original project)
  const initChatHandler = useCallback(async (_history: ChatHistory): Promise<string> => {
    try {
      // Note: isCreatingNewChat is already set to true in submitPrompt for new chats
      // No need to set it again here to avoid state conflicts
      
      // CRITICAL FIX: Only send history format to backend, not both formats
      // Create new chat using original project's structure
      const newChatData = {
        title: 'New Chat',
        models: selectedModels,
        history: _history,
        tags: [],
        timestamp: Date.now()
        // Removed messages array to avoid backend confusion
      };

      const newChat = await createChat(newChatData);
      
      if (newChat?.id && isValidChatId(newChat.id)) {
        // Update URL to match original project behavior
        window.history.replaceState(window.history.state, '', `/c/${newChat.id}`);
        
        // Update store
        setCurrentChat(newChat);
        
        // 注意: createChat 已经通过 createNewChatAndRefresh 调用了步骤1和步骤2
        // 步骤1: createNewChat (/api/v1/chats/new)
        // 步骤2: getChatList (/api/v1/chats/?page=1)
        // 所以这里不需要再次调用 loadChats
        
        return newChat.id;
      } else {
        throw new Error('Failed to create new chat');
      }
    } catch (error) {
      console.error('Error creating new chat:', error);
      toast.error('Failed to create new chat');
      throw error;
    } finally {
      // Always clear creating state
      console.log('✅ [initChatHandler] Setting isCreatingNewChat = false in finally block');
      setIsCreatingNewChat(false);
    }
  }, [chatId, selectedModels, createChat, isValidChatId, setCurrentChat, loadChats]);

  // Handle message submission (compatibility with existing MessageInput)
  const handleSubmit = useCallback(async (data: {
    prompt: string;
    files?: any[];
    selectedModels: string[];
  }) => {
    // Immediately clear input to provide instant feedback
    setPrompt('');
    setFiles([]);
    
    // Set files from data and submit
    setFiles(data.files || []);
    await submitPrompt(data.prompt);
  }, [submitPrompt]);

  // Send prompt to model via socket (like original project)
  const sendPromptSocket = useCallback(async (
    _history: ChatHistory,
    model: { id: string; name: string },
    responseMessageId: string,
    currentChatId: string,
    isFirstChat: boolean = false,
    originalCurrentId: string | null = null // Add parameter for original currentId
  ) => {
    const chatMessages = createMessagesList(_history, _history.currentId);
    const responseMessage = _history.messages[responseMessageId];
    const parentUserMessage = _history.messages[responseMessage.parentId!];

    // File processing exactly like original project
    const chatMessageFiles = chatMessages
      .filter((message) => message.files)
      .flatMap((message) => message.files);
    
    // Filter chatFiles to only include files that are in the chatMessageFiles (like original)
    // TODO: Implement chatFiles state management when available
    let processedFiles = [...files]; // Use current files state for now
    processedFiles = processedFiles.filter((item: any) => {
      const fileExists = chatMessageFiles.some((messageFile: any) => messageFile.id === item.id);
      return fileExists;
    });
    
    // Create files array exactly like original project
    let finalFiles = JSON.parse(JSON.stringify(processedFiles));
    finalFiles.push(
      ...(parentUserMessage?.files ?? []).filter((item: any) =>
        ['doc', 'file', 'collection'].includes(item.type)
      ),
      ...(responseMessage?.files ?? []).filter((item: any) => 
        ['web_search_results'].includes(item.type)
      )
    );
    
    // Remove duplicates like original project
    finalFiles = finalFiles.filter(
      (item: any, index: number, array: any[]) =>
        array.findIndex((i: any) => JSON.stringify(i) === JSON.stringify(item)) === index
    );

    // Calculate stream setting like original project
    const stream = 
      models.find((m) => m.id === model.id)?.info?.params?.stream_response ??
      // TODO: $settings?.params?.stream_response ?? when available
      // TODO: params?.stream_response ?? when available
      true;

    // Build messages exactly like original project with complete message processing
    
    // Get historical messages excluding the current user message
    const currentUserMsg = _history.messages[responseMessage.parentId!];
    const historicalMessages = currentUserMsg && currentUserMsg.parentId 
      ? createMessagesList(_history, currentUserMsg.parentId)
      : [];
    
    // Now add the current user message separately with the actual question
    const currentUserMessage = currentUserMsg ? {
      role: 'user' as const,
      content: currentUserMsg.content
    } : null;
    
    let messages = [
      // System message with strong instruction to not repeat user questions
      {
        role: 'system' as const,
        content: `You are a helpful AI assistant. 

IMPORTANT: Do NOT repeat, echo, or include the user's question in your response. Start your response directly with the answer. Never begin your response by restating what the user asked.

Example:
- User asks: "What is your name?"
- CORRECT response: "I am Claude, an AI assistant created by Anthropic."
- WRONG response: "What is your name? I am Claude, an AI assistant created by Anthropic."

Always respond directly without repeating the question.${
          (params as any)?.system || undefined /* TODO: $settings?.system */ ? 
          `\n\n${(params as any)?.system ?? undefined /* TODO: $settings?.system */ ?? ''}` : ''
        }${
          (responseMessage?.userContext ?? null)
            ? `\n\nUser Context:\n${responseMessage?.userContext ?? ''}`
            : ''
        }`
      },
      // Add historical messages (before current user message)
      ...historicalMessages.filter((message) => {
        // Filter out empty assistant messages from API request
        if (message.role === 'assistant' && (!message.content || message.content.trim() === '')) {
          return false;
        }
        return true;
      }).map((message) => ({
        ...message, // Include ALL message properties like original project
        content: message.content // TODO: processDetails(message.content) when available
      }))
    ];
    
    // Add current user message if exists
    if (currentUserMessage) {
      messages.push(currentUserMessage);
    }
    
    // Filter out any undefined messages
    messages = messages.filter((message) => message);

    // Process messages exactly like original project with complete mapping
    const apiMessages = messages
      .map((message, idx, arr) => ({
        role: message.role,
        // Handle image files like original project
        ...((message.files?.filter((file: any) => file.type === 'image').length > 0 ?? false) &&
        message.role === 'user'
          ? {
              content: [
                {
                  type: 'text',
                  text: (message as any)?.merged?.content ?? message.content
                },
                ...(message.files || [])
                  .filter((file: any) => file.type === 'image')
                  .map((file: any) => ({
                    type: 'image_url',
                    image_url: {
                      url: file.url
                    }
                  }))
              ]
            }
          : {
              content: (message as any)?.merged?.content ?? message.content
            })
      }))
      .filter((message) => message?.role === 'user' || message?.content?.trim());

    try {
      // Create abort controller for this response
      const abortController = new AbortController();
      setAbortControllers(prev => new Map(prev.set(responseMessageId, abortController)));

      // Ensure WebSocket is connected before sending chat request
      if (token && !isWebSocketConnected) {
        try {
          await connectWebSocket();
          // Wait a bit for connection to establish and get socket ID
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch {
          // Ignore WebSocket connection errors
        }
      }
      
      // Get the current socket state after potential connection
      const { socket: currentSocket } = useAppStore.getState();

      // Use fetch for streaming response
      
      const requestBody = {
        stream: stream,
        model: model.id,
        messages: apiMessages,
        params: {
            // TODO: Add ...$settings?.params when settings store is available
            ...params, // Use the params state variable like original project
            // Default params to match original project structure
            temperature: 0.7,
            max_tokens: 2000,
            top_p: 1.0,
            frequency_penalty: 0,
            presence_penalty: 0,
            format: undefined, // TODO: $settings.requestFormat ?? undefined
            keep_alive: undefined, // TODO: $settings.keepAlive ?? undefined
            stop: 
              // Implement exact original project stop token processing
              ((params as any)?.stop ?? undefined /* TODO: $settings?.params?.stop */ ?? undefined)
                ? (((params as any)?.stop?.split(',').map((token: string) => token.trim()) ?? undefined /* TODO: $settings.params.stop */) as string[])?.map(
                    (str: string) => decodeURIComponent(JSON.parse('"' + str.replace(/\"/g, '\\"') + '"'))
                  )
                : undefined
          },
          files: (finalFiles?.length ?? 0) > 0 ? finalFiles : undefined,
          tool_ids: selectedToolIds.length > 0 ? selectedToolIds : undefined,
          tool_servers: undefined, // TODO: Get $toolServers from store when available
          features: {
            // TODO: Implement complex permission checks like original project:
            // $config?.features?.enable_image_generation &&
            // ($user?.role === 'admin' || $user?.permissions?.features?.image_generation)
            //   ? imageGenerationEnabled : false
            image_generation: imageGenerationEnabled,
            code_interpreter: codeInterpreterEnabled,
            web_search: webSearchEnabled
            // TODO: Original project also has complex web_search logic:
            // webSearchEnabled || ($settings?.webSearch ?? false) === 'always'
          },
          variables: {
            ...getPromptVariables(
              undefined, // TODO: Get $user?.name when user store is available
              undefined  // TODO: Get user location with getAndUpdateUserLocation when available
              // Original project does:
              // $settings?.userLocation
              //   ? await getAndUpdateUserLocation(localStorage.token).catch((err) => {
              //       console.error(err);
              //       return undefined;
              //     })
              //   : undefined
            )
          },
          model_item: models.find((m) => m.id === model.id),
          session_id: currentSocket?.id || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          chat_id: currentChatId,
          id: originalCurrentId || _history.currentId, // Use original currentId from API, fallback to current
          // Background tasks with exact original project logic
          ...(!false && // temporaryChatEnabled is false for now
          (apiMessages.length == 1 ||
            (apiMessages.length == 2 &&
              apiMessages.at(0)?.role === 'system' &&
              apiMessages.at(1)?.role === 'user')) &&
          (selectedModels[0] === model.id) // First selected model triggers background tasks
            ? {
                background_tasks: {
                  title_generation: true, // Auto-generate titles for new chats
                  tags_generation: true // Auto-generate tags for new chats
                }
              }
            : {}),
          // Stream options with exact original project logic
          ...(stream && (models.find((m) => m.id === model.id)?.info?.meta?.capabilities?.usage ?? false)
            ? {
                stream_options: {
                  include_usage: true
                }
              }
            : {})
      };

      const response = await fetch(`${WEBUI_BASE_URL}/api/chat/completions`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      // Process simple HTTP response (not streaming)
      await response.json();

      // The response should contain task_id indicating the chat task has started
      // The actual chat content will be received via WebSocket events


    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
      } else {
        console.error('HTTP API error for model', model.id, ':', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        // Update message with error
        setHistory(prevHistory => {
          const updatedHistory = {
            ...prevHistory,
            messages: { ...prevHistory.messages }
          };
          if (updatedHistory.messages[responseMessageId]) {
            updatedHistory.messages[responseMessageId] = {
              ...updatedHistory.messages[responseMessageId],
              content: `Error: ${errorMessage}`,
              done: true,
              error: true
            };
          }
          return updatedHistory;
        });
      }
    } finally {
      // Clean up abort controller
      setAbortControllers(prev => {
        const newMap = new Map(prev);
        newMap.delete(responseMessageId);
        
        // Check if this was the last active controller
        if (newMap.size === 0) {
          // No more active requests, safe to set loading to false
          setIsLoading(false);
        } else {
        }
        
        return newMap;
      });
    }
  }, [token, chatCompletedHandler]);

  // Listen for WebSocket chat content events via custom DOM events
  useEffect(() => {
    const handleWebSocketChatContent = (event: CustomEvent) => {
      const eventData = event.detail;

      if (eventData.type === 'chat:completion' && eventData.data?.content) {
        // Update history with the received content
        setHistory(prevHistory => {
          const updatedHistory = {
            ...prevHistory,
            messages: { ...prevHistory.messages }
          };

          // Find the assistant message to update
          let messageIdToUpdate = null;

          // Strategy 1: Use exact message_id from WebSocket event - but ONLY if it's an assistant message
          if (eventData.message_id && updatedHistory.messages[eventData.message_id]) {
            const targetMessage = updatedHistory.messages[eventData.message_id];
            if (targetMessage.role === 'assistant') {
              messageIdToUpdate = eventData.message_id;
            }
          }

          // Strategy 2: Find the last assistant message that's not done
          if (!messageIdToUpdate) {
            const assistantMessages = Object.keys(updatedHistory.messages)
              .map(id => ({ id, msg: updatedHistory.messages[id] }))
              .filter(item => item.msg.role === 'assistant' && !item.msg.done)
              .sort((a, b) => b.msg.timestamp - a.msg.timestamp);

            if (assistantMessages.length > 0) {
              messageIdToUpdate = assistantMessages[0].id;
            }
          }

          if (messageIdToUpdate && updatedHistory.messages[messageIdToUpdate]) {
            const oldMessage = updatedHistory.messages[messageIdToUpdate];

            // Process the content to remove any accidental repetition of user questions
            let processedContent = eventData.data.content;

            // Find the most recent user message to check for repetition
            const userMessages = Object.keys(updatedHistory.messages)
              .map(id => updatedHistory.messages[id])
              .filter(msg => msg.role === 'user')
              .sort((a, b) => b.timestamp - a.timestamp);

            if (userMessages.length > 0) {
              const lastUserQuestion = userMessages[0].content;

              // Check if AI response starts with the user question
              if (processedContent.toLowerCase().startsWith(lastUserQuestion.toLowerCase())) {
                // Remove the user question from the beginning
                processedContent = processedContent.substring(lastUserQuestion.length).trim();
              }
            }

            // Create new message object to ensure React detects the change
            updatedHistory.messages[messageIdToUpdate] = {
              ...oldMessage,
              content: processedContent,
              done: eventData.data.done || false,
              timestamp: oldMessage.timestamp
            };

            // If this is the completion of the assistant's response, call chatCompletedHandler
            if (eventData.data.done) {
              // Check if we've already processed this message to prevent duplicate calls
              if (completedMessagesRef.current.has(messageIdToUpdate)) {
                return updatedHistory;
              }

              // Mark this message as being processed
              completedMessagesRef.current.add(messageIdToUpdate);

              // Call chatCompletedHandler asynchronously to avoid blocking the state update
              setTimeout(async () => {
                try {
                  // Get the current chat ID and model info
                  const currentChatId = eventData.chat_id;
                  const modelId = 'chatgpt-4o-latest'; // TODO: Get actual model ID from WebSocket event

                  await chatCompletedHandler(
                    currentChatId,
                    modelId,
                    messageIdToUpdate,
                    updatedHistory,
                    false, // isFirstChat
                    null // originalCurrentId
                  );
                } catch (error) {
                  console.error('Error calling chatCompletedHandler:', error);
                  // Remove from completed set if there was an error, so it can be retried
                  completedMessagesRef.current.delete(messageIdToUpdate);
                }
              }, 0);
            }
          } else {

          }

          return updatedHistory;
        });
      }
    };

    // Listen for the custom event
    window.addEventListener('websocket-chat-content', handleWebSocketChatContent as EventListener);

    return () => {
      window.removeEventListener('websocket-chat-content', handleWebSocketChatContent as EventListener);
    };
  }, [chatCompletedHandler]);

  // Handle prompt change
  const handlePromptChange = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);
  }, []);

  // Stop response generation (like original project)
  const stopResponse = useCallback(() => {
    // Cancel all active abort controllers
    abortControllers.forEach((controller) => {
      controller.abort();
    });

    // Clear abort controllers
    setAbortControllers(new Map());

    // Mark all incomplete messages as done in history
    setHistory(prevHistory => {
      const updatedHistory = {
        ...prevHistory,
        messages: { ...prevHistory.messages }
      };
      Object.keys(updatedHistory.messages).forEach(messageId => {
        const message = updatedHistory.messages[messageId];
        if (message.role === 'assistant' && !message.done) {
          updatedHistory.messages[messageId] = {
            ...message,
            done: true
          };
        }
      });
      return updatedHistory;
    });

    // Set loading to false
    setIsLoading(false);
  }, [abortControllers]);

  // Use isLoading as the primary indicator for generating state
  // This is more reliable than deriving from message history
  const isGenerating = isLoading;



  return (
    <>
      <div className={cn("flex flex-col h-full", className)}>
        {/* Messages area with bottom padding for fixed input when messages exist or loading history */}
        <div className={cn(
          "flex-1 overflow-hidden",
          (messages.length > 0 || isHistoryLoading || isCreatingNewChat || isSubmittingMessage) ? "pb-24" : ""
        )}>
          <Messages
            chatId={chatId}
            chat={currentChat}
            messages={messages}
            selectedModels={selectedModels}
            prompt={prompt}
            onPromptChange={handlePromptChange}
            onSubmit={handleSubmit}
            autoScroll={autoScroll}
            bottomPadding={messages.length > 0 || isHistoryLoading || isCreatingNewChat || isSubmittingMessage}
            temporaryChatEnabled={false}
            isHistoryLoading={isHistoryLoading}
            isCreatingNewChat={isCreatingNewChat}
            isSubmittingMessage={isSubmittingMessage}
          />
        </div>
      </div>

      {/* Fixed Message input area at bottom of viewport - show when we have messages OR in any loading state */}
      {(messages.length > 0 || isHistoryLoading || isCreatingNewChat || isSubmittingMessage) && (
        <div className={cn(
          "fixed bottom-0 right-0 z-50 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 transition-all duration-300",
          sidebarOpen ? "lg:left-80" : "left-0"
        )}>
          <div className="max-w-4xl mx-auto p-4">
            <MessageInput
              prompt={prompt}
              onPromptChange={handlePromptChange}
              onSubmit={handleSubmit}
              disabled={isLoading}
              selectedModels={selectedModels}
              files={files}
              onFilesChange={setFiles}
              placeholder="Type your message..."
              isGenerating={isGenerating}
              onStop={stopResponse}
            />
          </div>
        </div>
      )}
    </>
  );
};
